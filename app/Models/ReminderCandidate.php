<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;

class ReminderCandidate extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';

    protected $table = 'reminder_candidates';

    protected $fillable = [
        'user_id',
        'search_work',
        'last_reminder_sent_at'
    ];

    protected $casts = [
        'search_work' => 'boolean',
        'last_reminder_sent_at' => 'datetime',
    ];
}
