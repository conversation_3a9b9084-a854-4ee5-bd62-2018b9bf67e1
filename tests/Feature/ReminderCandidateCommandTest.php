<?php

namespace Tests\Feature;

use App\Console\Commands\ReminderCandidateCommand;
use App\Mail\ReminderCandidateMail;
use App\Models\Civility;
use App\Models\Role;
use App\Models\ReminderCandidate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class ReminderCandidateCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer le rôle candidat
        Role::create([
            'name' => 'Candidat',
            'slug' => 'candidate'
        ]);
    }

    /** @test */
    public function it_sends_first_reminder_after_12_days()
    {
        Mail::fake();

        // Créer un candidat inscrit il y a 12 jours
        $user = User::factory()->create([
            'role_id' => Role::where('slug', 'candidate')->first()->id,
            'created_at' => now()->subDays(12)
        ]);

        $this->artisan('app:reminder-candidate');

        // Vérifier qu'un reminder a été créé
        $this->assertDatabaseHas('reminder_candidates', [
            'user_id' => $user->id,
            'search_work' => null
        ]);

        // Vérifier qu'un email a été envoyé
        Mail::assertSent(ReminderCandidateMail::class);
    }

    /** @test */
    public function it_does_not_send_reminder_to_candidates_who_refused()
    {
        Mail::fake();

        $user = User::factory()->create([
            'role_id' => Role::where('slug', 'candidate')->first()->id,
            'created_at' => now()->subDays(30)
        ]);

        // Créer un reminder avec refus
        ReminderCandidate::create([
            'user_id' => $user->id,
            'search_work' => false,
            'last_reminder_sent_at' => now()->subDays(20)
        ]);

        $this->artisan('app:reminder-candidate');

        // Vérifier qu'aucun email n'a été envoyé
        Mail::assertNotSent(ReminderCandidateMail::class);
    }

    /** @test */
    public function it_sends_periodic_reminders_every_14_days_for_active_candidates()
    {
        Mail::fake();

        $user = User::factory()->create([
            'role_id' => Role::where('slug', 'candidate')->first()->id,
            'created_at' => now()->subDays(30)
        ]);

        // Créer un reminder avec acceptation, dernier rappel il y a 14 jours
        $reminder = ReminderCandidate::create([
            'user_id' => $user->id,
            'search_work' => true,
            'last_reminder_sent_at' => now()->subDays(14)
        ]);

        $this->artisan('app:reminder-candidate');

        // Vérifier qu'un email a été envoyé
        Mail::assertSent(ReminderCandidateMail::class);

        // Vérifier que la date du dernier rappel a été mise à jour
        $reminder->refresh();
        $this->assertTrue($reminder->last_reminder_sent_at->isToday());
    }

    /** @test */
    public function it_continues_sending_reminders_to_non_responsive_candidates()
    {
        Mail::fake();

        $user = User::factory()->create([
            'role_id' => Role::where('slug', 'candidate')->first()->id,
            'created_at' => now()->subDays(30)
        ]);

        // Créer un reminder sans réponse, dernier rappel il y a 14 jours
        ReminderCandidate::create([
            'user_id' => $user->id,
            'search_work' => null,
            'last_reminder_sent_at' => now()->subDays(14)
        ]);

        $this->artisan('app:reminder-candidate');

        // Vérifier qu'un email a été envoyé
        Mail::assertSent(ReminderCandidateMail::class);
    }
}
