<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reminder_candidates', function (Blueprint $table) {
            $table->timestamp('last_reminder_sent_at')->nullable()->after('search_work');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reminder_candidates', function (Blueprint $table) {
            $table->dropColumn('last_reminder_sent_at');
        });
    }
};
